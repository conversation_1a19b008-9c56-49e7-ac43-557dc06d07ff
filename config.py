"""
Configuration settings for Qwen-VL-Plus image processor
"""
import os
from dotenv import load_dotenv

load_dotenv()

# Qwen API Configuration
QWEN_API_KEY = os.getenv('QWEN_API_KEY', '')
QWEN_API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"

# Supported image formats
SUPPORTED_FORMATS = ['JPEG', 'JPG', 'PNG', 'BMP', 'WEBP']

# Question types mapping
QUESTION_TYPES = {
    '判断题': 'True/False',
    '单选题': 'Single Choice', 
    '多选题': 'Multiple Choice'
}

# Output template
OUTPUT_TEMPLATE = """题干类型：{question_type}
题干内容：{question_content}
题干选项A：{option_a}
题干选项B：{option_b}
题干选项C：{option_c}
题干选项D：{option_d}
图中图的解析：{image_analysis}"""
