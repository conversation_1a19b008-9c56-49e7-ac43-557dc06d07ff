帮我使用python协议个脚本。将图片发送给qwen-vl-plus模型。让他识别图片内容。然后将qwen返回的数据打印出来。

我一共有三种题目类型；
判断题、单选题、多选题；
这些题目都有自己的特征；
1. 判断题
  1.1 选项只有两个，Y：正确 与 N：错误
  1.2 答案只有一个

2. 单选题
  2.1 选项有A、B、C、D四个选项
  2.2 正确答案只有一个

3. 多选题
  3.1 选项有A、B、C、D四个选项
  3.2 正确答案有多个

他们都有共同特征；
1. 在图片中都有题目类型的声明；
2. 都有可能存在图中图，如果有图中图那这张图片肯定在选项的下方位置

我对qwen的要求，必须高精度的识别跟题目相关的每一个文字。如果存在题干图片的图中图，qwen必须要识别到并且做出正确的解析。我不要求qwen给我分析题目内容的答案。但是我需要qwen最终能严格给我输出固定格式的题目内容；
题干输出格式

题干类型：比如多选题/单选题/判断题
题干内容：完整的不包含序号的题目内容
题干选项A：选项A的内容
题干选项B：选项B的内容
题干选项C：选项C的内容
题干选项D：选项D的内容
图中图的解析：如果有图中图则生成图片解析，如果没有则不填内容


