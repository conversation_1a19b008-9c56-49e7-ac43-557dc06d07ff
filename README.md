# Qwen-VL-Plus 图片题目识别器

这是一个使用Qwen-VL-Plus模型识别图片中题目内容的Python脚本，专门用于处理判断题、单选题和多选题。

## 功能特点

- 🔍 **高精度识别**: 使用Qwen-VL-Plus模型进行图片文字识别
- 📝 **多题型支持**: 支持判断题、单选题、多选题三种题型
- 🖼️ **图中图解析**: 能够识别和解析题目中的图片内容
- 📋 **结构化输出**: 按照固定格式输出题目信息
- 🔄 **批量处理**: 支持单张图片和批量图片处理

## 支持的题型

### 1. 判断题
- 选项：Y（正确）和 N（错误）
- 答案：只有一个正确答案

### 2. 单选题
- 选项：A、B、C、D四个选项
- 答案：只有一个正确答案

### 3. 多选题
- 选项：A、B、C、D四个选项
- 答案：可能有多个正确答案

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制环境变量模板文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入你的Qwen API密钥：
```
QWEN_API_KEY=your_actual_api_key_here
```

## 使用方法

### 1. 命令行使用

```bash
python qwen_image_processor.py <图片路径>
```

示例：
```bash
python qwen_image_processor.py test_image.jpg
```

### 2. 作为模块使用

```python
from qwen_image_processor import QwenImageProcessor

# 初始化处理器
processor = QwenImageProcessor()

# 处理单张图片
result = processor.process_image("path/to/your/image.jpg")
print(result)
```

### 3. 批量处理

参考 `example_usage.py` 中的批量处理示例：

```python
python example_usage.py
```

## 输出格式

脚本会按照以下固定格式输出题目信息：

```
题干类型：[判断题/单选题/多选题]
题干内容：[完整的题目内容，不包含序号]
题干选项A：[选项A的内容]
题干选项B：[选项B的内容]
题干选项C：[选项C的内容，如果没有则留空]
题干选项D：[选项D的内容，如果没有则留空]
图中图的解析：[如果存在图中图则详细描述，如果没有则留空]
```

## 支持的图片格式

- JPEG/JPG
- PNG
- BMP
- WEBP

## 文件结构

```
.
├── qwen_image_processor.py    # 主处理脚本
├── config.py                  # 配置文件
├── example_usage.py          # 使用示例
├── requirements.txt          # 依赖包列表
├── .env.example             # 环境变量模板
├── README.md                # 说明文档
└── sq.md                    # 原始需求文档
```

## 注意事项

1. **API密钥**: 确保正确设置Qwen API密钥
2. **网络连接**: 需要稳定的网络连接来调用API
3. **图片质量**: 建议使用清晰的图片以获得更好的识别效果
4. **API限制**: 注意API调用频率和配额限制
5. **图片大小**: 脚本会自动压缩大图片以减少API调用成本

## 错误处理

脚本包含完善的错误处理机制：

- 图片格式验证
- API调用异常处理
- 响应解析错误处理
- 网络连接超时处理

## 示例输出

```
题干类型：单选题
题干内容：下列哪个选项是Python的特点？
题干选项A：编译型语言
题干选项B：解释型语言
题干选项C：汇编语言
题干选项D：机器语言
图中图的解析：
```

## 开发者信息

如需修改或扩展功能，请参考代码中的注释和文档字符串。主要的类和方法：

- `QwenImageProcessor`: 主处理类
- `encode_image()`: 图片编码方法
- `call_qwen_api()`: API调用方法
- `parse_response()`: 响应解析方法
- `process_image()`: 主处理方法
