"""
使用示例：Qwen-VL-Plus图片处理器
"""
from qwen_image_processor import QwenImageProcessor
import os


def example_single_image():
    """
    单张图片处理示例
    """
    # 初始化处理器
    processor = QwenImageProcessor()
    
    # 处理图片
    image_path = "test_image.jpg"  # 替换为你的图片路径
    
    if not os.path.exists(image_path):
        print(f"图片文件不存在: {image_path}")
        return
    
    try:
        result = processor.process_image(image_path)
        print("识别结果:")
        print("-" * 50)
        print(result)
        print("-" * 50)
    except Exception as e:
        print(f"处理失败: {str(e)}")


def example_batch_processing():
    """
    批量处理示例
    """
    processor = QwenImageProcessor()
    
    # 图片文件夹路径
    image_folder = "images"  # 替换为你的图片文件夹路径
    
    if not os.path.exists(image_folder):
        print(f"图片文件夹不存在: {image_folder}")
        return
    
    # 支持的图片格式
    supported_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
    
    # 获取所有图片文件
    image_files = []
    for file in os.listdir(image_folder):
        if any(file.lower().endswith(ext) for ext in supported_extensions):
            image_files.append(os.path.join(image_folder, file))
    
    if not image_files:
        print("未找到支持的图片文件")
        return
    
    print(f"找到 {len(image_files)} 张图片，开始批量处理...")
    
    results = []
    for i, image_path in enumerate(image_files, 1):
        print(f"\n处理第 {i}/{len(image_files)} 张图片: {os.path.basename(image_path)}")
        try:
            result = processor.process_image(image_path)
            results.append({
                'file': os.path.basename(image_path),
                'result': result,
                'status': 'success'
            })
            print("✓ 处理成功")
        except Exception as e:
            results.append({
                'file': os.path.basename(image_path),
                'error': str(e),
                'status': 'failed'
            })
            print(f"✗ 处理失败: {str(e)}")
    
    # 输出汇总结果
    print("\n" + "="*60)
    print("批量处理结果汇总")
    print("="*60)
    
    success_count = sum(1 for r in results if r['status'] == 'success')
    failed_count = len(results) - success_count
    
    print(f"总计: {len(results)} 张图片")
    print(f"成功: {success_count} 张")
    print(f"失败: {failed_count} 张")
    
    if failed_count > 0:
        print("\n失败的文件:")
        for result in results:
            if result['status'] == 'failed':
                print(f"  - {result['file']}: {result['error']}")
    
    print("\n成功处理的结果:")
    for result in results:
        if result['status'] == 'success':
            print(f"\n文件: {result['file']}")
            print("-" * 40)
            print(result['result'])
            print("-" * 40)


def example_custom_api_key():
    """
    使用自定义API密钥的示例
    """
    # 直接传入API密钥（不推荐在生产环境中硬编码）
    api_key = "your_api_key_here"
    processor = QwenImageProcessor(api_key=api_key)
    
    # 其他处理逻辑同上...


if __name__ == "__main__":
    print("Qwen-VL-Plus图片处理器使用示例")
    print("请确保已设置QWEN_API_KEY环境变量")
    print()
    
    # 检查API密钥
    if not os.getenv('QWEN_API_KEY'):
        print("警告: 未设置QWEN_API_KEY环境变量")
        print("请在.env文件中设置或通过环境变量设置")
        print()
    
    # 运行单张图片处理示例
    print("1. 单张图片处理示例:")
    example_single_image()
    
    print("\n" + "="*60)
    
    # 运行批量处理示例
    print("2. 批量处理示例:")
    example_batch_processing()
