"""
Qwen-VL-Plus Image Processor
处理图片并使用Qwen-VL-Plus模型识别题目内容
"""
import base64
import json
import requests
from PIL import Image
import io
from typing import Dict, Optional, Tuple
from config import QWEN_API_KEY, QWEN_API_URL, SUPPORTED_FORMATS, OUTPUT_TEMPLATE


class QwenImageProcessor:
    def __init__(self, api_key: str = None):
        """
        初始化Qwen图片处理器
        
        Args:
            api_key: Qwen API密钥
        """
        self.api_key = api_key or QWEN_API_KEY
        if not self.api_key:
            raise ValueError("请设置QWEN_API_KEY环境变量或传入api_key参数")
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def encode_image(self, image_path: str) -> str:
        """
        将图片编码为base64格式
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            base64编码的图片字符串
        """
        try:
            with Image.open(image_path) as img:
                # 验证图片格式
                if img.format not in SUPPORTED_FORMATS:
                    raise ValueError(f"不支持的图片格式: {img.format}")
                
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 压缩图片以减少API调用成本（可选）
                max_size = (1024, 1024)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # 编码为base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_str = base64.b64encode(buffer.getvalue()).decode()
                
                return img_str
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")
    
    def create_prompt(self) -> str:
        """
        创建用于Qwen-VL-Plus的提示词
        
        Returns:
            格式化的提示词
        """
        prompt = """请仔细分析这张图片中的题目内容。这是一道考试题目，可能是以下三种类型之一：

1. 判断题：选项只有Y（正确）和N（错误）两个，答案只有一个
2. 单选题：选项有A、B、C、D四个，正确答案只有一个  
3. 多选题：选项有A、B、C、D四个，正确答案有多个

请注意：
- 图片中会明确标注题目类型
- 可能存在图中图（通常在选项下方），如果有请详细解析
- 请高精度识别所有文字内容
- 不需要分析答案，只需要提取题目信息

请严格按照以下格式输出：
题干类型：[判断题/单选题/多选题]
题干内容：[完整的题目内容，不包含序号]
题干选项A：[选项A的内容，如果是判断题则为Y或N对应的内容]
题干选项B：[选项B的内容，如果是判断题则为Y或N对应的内容]
题干选项C：[选项C的内容，如果没有则留空]
题干选项D：[选项D的内容，如果没有则留空]
图中图的解析：[如果存在图中图则详细描述，如果没有则留空]"""
        
        return prompt

    def call_qwen_api(self, image_base64: str, prompt: str) -> Dict:
        """
        调用Qwen-VL-Plus API

        Args:
            image_base64: base64编码的图片
            prompt: 提示词

        Returns:
            API响应结果
        """
        payload = {
            "model": "qwen-vl-plus",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "image": f"data:image/jpeg;base64,{image_base64}"
                            },
                            {
                                "text": prompt
                            }
                        ]
                    }
                ]
            },
            "parameters": {
                "result_format": "message"
            }
        }

        try:
            response = requests.post(
                QWEN_API_URL,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API调用失败: {str(e)}")

    def parse_response(self, response: Dict) -> Dict[str, str]:
        """
        解析Qwen API响应

        Args:
            response: API响应

        Returns:
            解析后的题目信息字典
        """
        try:
            # 打印原始响应用于调试
            print(f"原始API响应: {response}")

            # 提取响应文本
            output = response.get('output', {})
            choices = output.get('choices', [])
            if not choices:
                raise ValueError("API响应中没有找到choices")

            message = choices[0].get('message', {})
            content = message.get('content', '')

            # 处理content可能是列表的情况
            if isinstance(content, list):
                # 如果content是列表，提取文本内容
                content_text = ""
                for item in content:
                    if isinstance(item, dict) and 'text' in item:
                        content_text += item['text']
                    elif isinstance(item, str):
                        content_text += item
                content = content_text

            if not content:
                raise ValueError("API响应内容为空")

            print(f"提取的内容: {content}")

            # 解析结构化输出
            result = {
                'question_type': '',
                'question_content': '',
                'option_a': '',
                'option_b': '',
                'option_c': '',
                'option_d': '',
                'image_analysis': ''
            }

            lines = content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('题干类型：'):
                    result['question_type'] = line.replace('题干类型：', '').strip()
                elif line.startswith('题干内容：'):
                    result['question_content'] = line.replace('题干内容：', '').strip()
                elif line.startswith('题干选项A：'):
                    result['option_a'] = line.replace('题干选项A：', '').strip()
                elif line.startswith('题干选项B：'):
                    result['option_b'] = line.replace('题干选项B：', '').strip()
                elif line.startswith('题干选项C：'):
                    result['option_c'] = line.replace('题干选项C：', '').strip()
                elif line.startswith('题干选项D：'):
                    result['option_d'] = line.replace('题干选项D：', '').strip()
                elif line.startswith('图中图的解析：'):
                    result['image_analysis'] = line.replace('图中图的解析：', '').strip()

            return result

        except Exception as e:
            raise Exception(f"响应解析失败: {str(e)}")

    def process_image(self, image_path: str) -> str:
        """
        处理图片并返回格式化结果

        Args:
            image_path: 图片文件路径

        Returns:
            格式化的题目信息
        """
        print(f"正在处理图片: {image_path}")

        # 1. 编码图片
        print("编码图片...")
        image_base64 = self.encode_image(image_path)

        # 2. 创建提示词
        prompt = self.create_prompt()

        # 3. 调用API
        print("调用Qwen-VL-Plus API...")
        response = self.call_qwen_api(image_base64, prompt)

        # 4. 解析响应
        print("解析API响应...")
        parsed_result = self.parse_response(response)

        # 5. 格式化输出
        formatted_output = OUTPUT_TEMPLATE.format(
            question_type=parsed_result['question_type'],
            question_content=parsed_result['question_content'],
            option_a=parsed_result['option_a'],
            option_b=parsed_result['option_b'],
            option_c=parsed_result['option_c'],
            option_d=parsed_result['option_d'],
            image_analysis=parsed_result['image_analysis']
        )

        return formatted_output


def main():
    """
    主函数示例
    """
    import sys

    if len(sys.argv) != 2:
        print("使用方法: python qwen_image_processor.py <图片路径>")
        sys.exit(1)

    image_path = sys.argv[1]

    try:
        processor = QwenImageProcessor()
        result = processor.process_image(image_path)
        print("\n" + "="*50)
        print("题目识别结果:")
        print("="*50)
        print(result)
        print("="*50)

    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
