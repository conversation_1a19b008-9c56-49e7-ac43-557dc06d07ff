"""
Qwen-VL-Plus Image Processor
处理图片并使用Qwen-VL-Plus模型识别题目内容
"""
import base64
import json
import requests
from PIL import Image
import io
from typing import Dict, Optional, Tuple
from config import QWEN_API_KEY, QWEN_API_URL, SUPPORTED_FORMATS, OUTPUT_TEMPLATE


class QwenImageProcessor:
    def __init__(self, api_key: str = None):
        """
        初始化Qwen图片处理器
        
        Args:
            api_key: Qwen API密钥
        """
        self.api_key = api_key or QWEN_API_KEY
        if not self.api_key:
            raise ValueError("请设置QWEN_API_KEY环境变量或传入api_key参数")
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def encode_image(self, image_path: str) -> str:
        """
        将图片编码为base64格式
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            base64编码的图片字符串
        """
        try:
            with Image.open(image_path) as img:
                # 验证图片格式
                if img.format not in SUPPORTED_FORMATS:
                    raise ValueError(f"不支持的图片格式: {img.format}")
                
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 压缩图片以减少API调用成本（可选）
                max_size = (1024, 1024)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # 编码为base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_str = base64.b64encode(buffer.getvalue()).decode()
                
                return img_str
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")
    
    def create_prompt(self) -> str:
        """
        创建用于Qwen-VL-Plus的提示词

        Returns:
            格式化的提示词
        """
        prompt = """请非常仔细地分析这张图片中的题目内容。这是一道考试题目，可能是以下三种类型之一：

1. 判断题：选项只有Y（正确）和N（错误）两个，答案只有一个
2. 单选题：选项有A、B、C、D四个，正确答案只有一个
3. 多选题：选项有A、B、C、D四个，正确答案有多个

**关键要求 - 图中图识别**：
这张图片很可能包含图中图！请务必仔细查看：
- 题目文字下方是否有图片
- 选项文字下方是否有图片
- 题目中间是否嵌入了图片
- 任何看起来像照片、示意图、标志、场景图的内容

**图中图通常包含**：
- 驾驶题：道路场景、交通情况、车辆、行人、交通标志、路况
- 安全题：危险场景、安全标识、工作环境
- 技术题：设备图片、操作示意图、工具图片

**分析步骤**：
1. 扫描整张图片，寻找除了文字以外的所有图像内容
2. 识别题目类型和文字内容
3. 提取各个选项
4. **重点**：详细描述你看到的任何图片内容，包括：
   - 场景描述（室内/室外、道路/建筑等）
   - 物体描述（车辆、人员、设备、标志等）
   - 颜色、形状、位置关系
   - 图片中的任何文字或标识

**重要**：请严格按照以下格式输出，不要添加任何额外的分析或解释：

题干类型：[判断题/单选题/多选题]
题干内容：[完整的题目内容，不包含序号]
题干选项A：[选项A的内容]
题干选项B：[选项B的内容]
题干选项C：[选项C的内容，如果没有则留空]
题干选项D：[选项D的内容，如果没有则留空]
图中图的解析：[详细描述图片中除文字外的所有图像内容。如果真的没有任何图片，才填写"无"]

注意：只输出上述格式的内容，不要添加任何其他文字、分析或建议！"""

        return prompt

    def call_qwen_api(self, image_base64: str, prompt: str) -> Dict:
        """
        调用Qwen-VL-Plus API

        Args:
            image_base64: base64编码的图片
            prompt: 提示词

        Returns:
            API响应结果
        """
        payload = {
            "model": "qwen-vl-plus",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "image": f"data:image/jpeg;base64,{image_base64}"
                            },
                            {
                                "text": prompt
                            }
                        ]
                    }
                ]
            },
            "parameters": {
                "result_format": "message"
            }
        }

        try:
            response = requests.post(
                QWEN_API_URL,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API调用失败: {str(e)}")

    def parse_response(self, response: Dict) -> tuple[Dict[str, str], Dict[str, int]]:
        """
        解析Qwen API响应

        Args:
            response: API响应

        Returns:
            tuple: (解析后的题目信息字典, token使用情况字典)
        """
        try:
            # 提取token使用情况
            usage = response.get('usage', {})
            token_info = {
                'total_tokens': usage.get('total_tokens', 0),
                'input_tokens': usage.get('input_tokens', 0),
                'output_tokens': usage.get('output_tokens', 0),
                'image_tokens': usage.get('image_tokens', 0),
                'text_tokens': usage.get('input_tokens_details', {}).get('text_tokens', 0)
            }

            # 提取响应文本
            output = response.get('output', {})
            choices = output.get('choices', [])
            if not choices:
                raise ValueError("API响应中没有找到choices")

            message = choices[0].get('message', {})
            content = message.get('content', '')

            # 处理content可能是列表的情况
            if isinstance(content, list):
                # 如果content是列表，提取文本内容
                content_text = ""
                for item in content:
                    if isinstance(item, dict) and 'text' in item:
                        content_text += item['text']
                    elif isinstance(item, str):
                        content_text += item
                content = content_text

            if not content:
                raise ValueError("API响应内容为空")





            # 解析结构化输出
            result = {
                'question_type': '',
                'question_content': '',
                'option_a': '',
                'option_b': '',
                'option_c': '',
                'option_d': '',
                'image_analysis': ''
            }

            lines = content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('题干类型：'):
                    result['question_type'] = line.replace('题干类型：', '').strip()
                elif line.startswith('题干内容：'):
                    result['question_content'] = line.replace('题干内容：', '').strip()
                elif (line.startswith('题干选项A') or line.startswith('题干选项 A') or
                      line.startswith('选项 A') or line.startswith('A.') or
                      line.startswith('A：') or line.startswith('A:')):
                    # 处理多种可能的格式
                    if '：' in line:
                        result['option_a'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_a'] = line.split(':', 1)[1].strip()
                    elif line.startswith('A.'):
                        result['option_a'] = line.replace('A.', '').strip()
                elif (line.startswith('题干选项B') or line.startswith('题干选项 B') or
                      line.startswith('选项 B') or line.startswith('B.') or
                      line.startswith('B：') or line.startswith('B:')):
                    if '：' in line:
                        result['option_b'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_b'] = line.split(':', 1)[1].strip()
                    elif line.startswith('B.'):
                        result['option_b'] = line.replace('B.', '').strip()
                elif (line.startswith('题干选项C') or line.startswith('题干选项 C') or
                      line.startswith('选项 C') or line.startswith('C.') or
                      line.startswith('C：') or line.startswith('C:')):
                    if '：' in line:
                        result['option_c'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_c'] = line.split(':', 1)[1].strip()
                    elif line.startswith('C.'):
                        result['option_c'] = line.replace('C.', '').strip()
                elif (line.startswith('题干选项D') or line.startswith('题干选项 D') or
                      line.startswith('选项 D') or line.startswith('D.') or
                      line.startswith('D：') or line.startswith('D:')):
                    if '：' in line:
                        result['option_d'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_d'] = line.split(':', 1)[1].strip()
                    elif line.startswith('D.'):
                        result['option_d'] = line.replace('D.', '').strip()
                elif line.startswith('图中图的解析：') or line.startswith('图中图的解析:'):
                    if '：' in line:
                        result['image_analysis'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['image_analysis'] = line.split(':', 1)[1].strip()

            return result, token_info

        except Exception as e:
            raise Exception(f"响应解析失败: {str(e)}")

    def process_image(self, image_path: str) -> str:
        """
        处理图片并返回格式化结果

        Args:
            image_path: 图片文件路径

        Returns:
            格式化的题目信息
        """
        print(f"正在处理图片: {image_path}")

        # 1. 编码图片
        print("编码图片...")
        image_base64 = self.encode_image(image_path)

        # 2. 创建提示词
        prompt = self.create_prompt()

        # 3. 调用API
        print("调用Qwen-VL-Plus API...")
        response = self.call_qwen_api(image_base64, prompt)

        # 4. 解析响应
        print("解析API响应...")
        parsed_result, token_info = self.parse_response(response)

        # 5. 显示token使用情况
        print(f"Token使用情况:")
        print(f"  总计: {token_info['total_tokens']} tokens")
        print(f"  输入: {token_info['input_tokens']} tokens (文本: {token_info['text_tokens']}, 图片: {token_info['image_tokens']})")
        print(f"  输出: {token_info['output_tokens']} tokens")

        # 6. 格式化输出
        formatted_output = OUTPUT_TEMPLATE.format(
            question_type=parsed_result['question_type'],
            question_content=parsed_result['question_content'],
            option_a=parsed_result['option_a'],
            option_b=parsed_result['option_b'],
            option_c=parsed_result['option_c'],
            option_d=parsed_result['option_d'],
            image_analysis=parsed_result['image_analysis']
        )

        return formatted_output


def main():
    """
    主函数示例
    """
    import sys

    if len(sys.argv) != 2:
        print("使用方法: python qwen_image_processor.py <图片路径>")
        sys.exit(1)

    image_path = sys.argv[1]

    try:
        processor = QwenImageProcessor()
        result = processor.process_image(image_path)
        print("\n" + "="*50)
        print("题目识别结果:")
        print("="*50)
        print(result)
        print("="*50)

    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
