"""
Qwen-VL-Plus Image Processor
处理图片并使用Qwen-VL-Plus模型识别题目内容
"""
import base64
import json
import requests
from PIL import Image
import io
from typing import Dict, Optional, Tuple
from config import QWEN_API_KEY, QWEN_API_URL, SUPPORTED_FORMATS, OUTPUT_TEMPLATE


class QwenImageProcessor:
    def __init__(self, api_key: str = None):
        """
        初始化Qwen图片处理器
        
        Args:
            api_key: Qwen API密钥
        """
        self.api_key = api_key or QWEN_API_KEY
        if not self.api_key:
            raise ValueError("请设置QWEN_API_KEY环境变量或传入api_key参数")
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def encode_image(self, image_path: str) -> str:
        """
        将图片编码为base64格式
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            base64编码的图片字符串
        """
        try:
            with Image.open(image_path) as img:
                # 验证图片格式
                if img.format not in SUPPORTED_FORMATS:
                    raise ValueError(f"不支持的图片格式: {img.format}")
                
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 压缩图片以减少API调用成本（可选）
                max_size = (1024, 1024)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # 编码为base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_str = base64.b64encode(buffer.getvalue()).decode()
                
                return img_str
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")
    
    def create_prompt(self) -> str:
        """
        创建用于Qwen-VL-Plus的提示词

        Returns:
            格式化的提示词
        """
        prompt = """**严格执行以下四个原则：**

**第一原则：完整精准识别文字**
- 必须识别图片中的每一个文字，不得遗漏
- 包括题目、选项、图片中的文字标识
- 确保文字识别的准确性

**第二原则：分析题干图片**
- 如果存在题干图片（图中图），必须详细分析
- 描述图片中的场景、物体、标志、文字等所有可见内容
- 不得忽略任何图像信息

**第三原则：严格遵循格式**
- 必须按照指定格式输出，不得偏离
- 格式如下：

题干类型：[判断题/单选题/多选题]
题干内容：[完整题目内容]
题干选项A：[选项A内容]
题干选项B：[选项B内容]
题干选项C：[选项C内容，无则留空]
题干选项D：[选项D内容，无则留空]
图中图的解析：[详细描述图片内容，无则填"无"]

**第四原则：禁止题意分析**
- 严禁输出题目分析、解题思路、答案推理
- 严禁给出正确答案或建议
- 只提取题目信息，不做任何分析

**立即开始执行，严格遵循四个原则！**"""

        return prompt

    def call_qwen_api(self, image_base64: str, prompt: str) -> Dict:
        """
        调用Qwen-VL-Plus API

        Args:
            image_base64: base64编码的图片
            prompt: 提示词

        Returns:
            API响应结果
        """
        payload = {
            "model": "qwen-vl-plus",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "image": f"data:image/jpeg;base64,{image_base64}"
                            },
                            {
                                "text": prompt
                            }
                        ]
                    }
                ]
            },
            "parameters": {
                "result_format": "message"
            }
        }

        try:
            response = requests.post(
                QWEN_API_URL,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API调用失败: {str(e)}")

    def parse_response(self, response: Dict) -> tuple[Dict[str, str], Dict[str, int]]:
        """
        解析Qwen API响应

        Args:
            response: API响应

        Returns:
            tuple: (解析后的题目信息字典, token使用情况字典)
        """
        try:
            # 提取token使用情况
            usage = response.get('usage', {})
            token_info = {
                'total_tokens': usage.get('total_tokens', 0),
                'input_tokens': usage.get('input_tokens', 0),
                'output_tokens': usage.get('output_tokens', 0),
                'image_tokens': usage.get('image_tokens', 0),
                'text_tokens': usage.get('input_tokens_details', {}).get('text_tokens', 0)
            }

            # 提取响应文本
            output = response.get('output', {})
            choices = output.get('choices', [])
            if not choices:
                raise ValueError("API响应中没有找到choices")

            message = choices[0].get('message', {})
            content = message.get('content', '')

            # 处理content可能是列表的情况
            if isinstance(content, list):
                # 如果content是列表，提取文本内容
                content_text = ""
                for item in content:
                    if isinstance(item, dict) and 'text' in item:
                        content_text += item['text']
                    elif isinstance(item, str):
                        content_text += item
                content = content_text

            if not content:
                raise ValueError("API响应内容为空")





            # 解析结构化输出
            result = {
                'question_type': '',
                'question_content': '',
                'option_a': '',
                'option_b': '',
                'option_c': '',
                'option_d': '',
                'image_analysis': ''
            }

            lines = content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('题干类型：'):
                    result['question_type'] = line.replace('题干类型：', '').strip()
                elif line.startswith('题干内容：'):
                    result['question_content'] = line.replace('题干内容：', '').strip()
                elif (line.startswith('题干选项A') or line.startswith('题干选项 A') or
                      line.startswith('选项 A') or line.startswith('A.') or
                      line.startswith('A：') or line.startswith('A:')):
                    # 处理多种可能的格式
                    if '：' in line:
                        result['option_a'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_a'] = line.split(':', 1)[1].strip()
                    elif line.startswith('A.'):
                        result['option_a'] = line.replace('A.', '').strip()
                elif (line.startswith('题干选项B') or line.startswith('题干选项 B') or
                      line.startswith('选项 B') or line.startswith('B.') or
                      line.startswith('B：') or line.startswith('B:')):
                    if '：' in line:
                        result['option_b'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_b'] = line.split(':', 1)[1].strip()
                    elif line.startswith('B.'):
                        result['option_b'] = line.replace('B.', '').strip()
                elif (line.startswith('题干选项C') or line.startswith('题干选项 C') or
                      line.startswith('选项 C') or line.startswith('C.') or
                      line.startswith('C：') or line.startswith('C:')):
                    if '：' in line:
                        result['option_c'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_c'] = line.split(':', 1)[1].strip()
                    elif line.startswith('C.'):
                        result['option_c'] = line.replace('C.', '').strip()
                elif (line.startswith('题干选项D') or line.startswith('题干选项 D') or
                      line.startswith('选项 D') or line.startswith('D.') or
                      line.startswith('D：') or line.startswith('D:')):
                    if '：' in line:
                        result['option_d'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['option_d'] = line.split(':', 1)[1].strip()
                    elif line.startswith('D.'):
                        result['option_d'] = line.replace('D.', '').strip()
                elif line.startswith('图中图的解析：') or line.startswith('图中图的解析:'):
                    if '：' in line:
                        result['image_analysis'] = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        result['image_analysis'] = line.split(':', 1)[1].strip()

            return result, token_info

        except Exception as e:
            raise Exception(f"响应解析失败: {str(e)}")

    def process_image(self, image_path: str) -> str:
        """
        处理图片并返回Qwen原始响应

        Args:
            image_path: 图片文件路径

        Returns:
            Qwen API的原始响应内容
        """
        print(f"正在处理图片: {image_path}")

        # 1. 编码图片
        print("编码图片...")
        image_base64 = self.encode_image(image_path)

        # 2. 创建提示词
        prompt = self.create_prompt()

        # 3. 调用API
        print("调用Qwen-VL-Plus API...")
        response = self.call_qwen_api(image_base64, prompt)

        # 4. 提取token使用情况
        usage = response.get('usage', {})
        print(f"Token使用情况:")
        print(f"  总计: {usage.get('total_tokens', 0)} tokens")
        print(f"  输入: {usage.get('input_tokens', 0)} tokens")
        print(f"  输出: {usage.get('output_tokens', 0)} tokens")
        print(f"  图片: {usage.get('image_tokens', 0)} tokens")

        # 5. 提取并返回原始内容
        output = response.get('output', {})
        choices = output.get('choices', [])
        if choices:
            message = choices[0].get('message', {})
            content = message.get('content', '')

            # 处理content可能是列表的情况
            if isinstance(content, list):
                content_text = ""
                for item in content:
                    if isinstance(item, dict) and 'text' in item:
                        content_text += item['text']
                    elif isinstance(item, str):
                        content_text += item
                content = content_text

            return content
        else:
            return "API响应中没有找到内容"


def main():
    """
    主函数示例
    """
    import sys

    if len(sys.argv) != 2:
        print("使用方法: python qwen_image_processor.py <图片路径>")
        sys.exit(1)

    image_path = sys.argv[1]

    try:
        processor = QwenImageProcessor()
        result = processor.process_image(image_path)
        print("\n" + "="*50)
        print("Qwen原始响应内容:")
        print("="*50)
        print(result)
        print("="*50)

    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
