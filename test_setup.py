"""
测试脚本：验证环境配置和依赖安装
"""
import sys
import os


def test_dependencies():
    """测试依赖包是否正确安装"""
    print("检查依赖包...")
    
    required_packages = {
        'requests': 'requests',
        'PIL': 'Pillow', 
        'dotenv': 'python-dotenv'
    }
    
    missing_packages = []
    
    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            print(f"✗ {package_name} 未安装")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✓ 所有依赖包已正确安装")
    return True


def test_config():
    """测试配置文件"""
    print("\n检查配置...")
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✓ .env 文件存在")
        
        # 检查API密钥
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('QWEN_API_KEY')
        if api_key and api_key != 'your_qwen_api_key_here':
            print("✓ QWEN_API_KEY 已设置")
            return True
        else:
            print("✗ QWEN_API_KEY 未正确设置")
            print("请在.env文件中设置正确的API密钥")
            return False
    else:
        print("✗ .env 文件不存在")
        print("请复制 .env.example 为 .env 并设置API密钥")
        return False


def test_import():
    """测试主模块导入"""
    print("\n检查主模块...")
    
    try:
        from qwen_image_processor import QwenImageProcessor
        print("✓ qwen_image_processor 模块导入成功")
        
        # 尝试初始化（不调用API）
        try:
            processor = QwenImageProcessor()
            print("✓ QwenImageProcessor 初始化成功")
            return True
        except ValueError as e:
            if "API密钥" in str(e):
                print("✗ API密钥未设置，但模块结构正常")
                return False
            else:
                print(f"✗ 初始化失败: {e}")
                return False
        except Exception as e:
            print(f"✗ 初始化失败: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Qwen-VL-Plus 图片处理器 - 环境测试")
    print("=" * 50)
    
    tests = [
        ("依赖包检查", test_dependencies),
        ("配置检查", test_config), 
        ("模块导入检查", test_import)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！环境配置正确，可以开始使用。")
        print("\n使用方法:")
        print("python qwen_image_processor.py <图片路径>")
        print("或参考 example_usage.py 中的示例代码")
    else:
        print("❌ 部分测试失败，请根据上述提示修复问题。")
        print("\n常见问题解决方案:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 配置API密钥: 复制 .env.example 为 .env 并设置正确的API密钥")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
